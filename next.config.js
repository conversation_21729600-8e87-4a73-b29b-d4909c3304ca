/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  transpilePackages: ['framer-motion'],
  experimental: {
    // This helps with handling ESM modules
    esmExternals: 'loose'
  },
  async rewrites() {
    return [
      {
        source: '/trade/static/:path*',
        destination: 'https://eu-assets.i.posthog.com/static/:path*',
      },
      {
        source: '/trade/:path*',
        destination: 'https://eu.i.posthog.com/:path*',
      },
      {
        source: '/trade/decide',
        destination: 'https://eu.i.posthog.com/decide',
      },
    ];
  },
  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,
};

module.exports = nextConfig;
