import type { AnalyticsProvider, AnalyticsConfig } from './types'

/**
 * Analytics Manager
 * Centralized analytics system that manages multiple providers
 */
export class AnalyticsManager {
  private providers: Map<string, AnalyticsProvider> = new Map()
  private eventQueue: Array<{ method: 'track'; args: [string, Record<string, any>?] } | { method: 'identify'; args: [Record<string, any>] } | { method: 'setUserId'; args: [string] }> = []
  private isInitialized = false
  private config: AnalyticsConfig

  constructor(config: AnalyticsConfig) {
    this.config = config
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return

    // Initialize all enabled providers
    for (const providerConfig of this.config.providers) {
      if (!providerConfig.enabled) continue
      
      try {
        const provider = await this.createProvider(providerConfig.name)
        if (provider) {
          await provider.initialize(providerConfig.config)
          this.providers.set(provider.name, provider)
          
          if (this.config.debug) {
            console.log(`[Analytics] Initialized provider: ${provider.name}`)
          }
        }
      } catch (error) {
        console.error(`[Analytics] Failed to initialize ${providerConfig.name}:`, error)
      }
    }

    // Process queued events
    await this.processEventQueue()
    this.isInitialized = true

    if (this.config.debug) {
      console.log(`[Analytics] Manager initialized with ${this.providers.size} providers`)
    }
  }

  // Primary tracking methods
  async track(event: string, properties?: Record<string, any>): Promise<void> {
    if (!this.config.enabled) return

    if (!this.isInitialized) {
      this.eventQueue.push({ method: 'track', args: [event, properties] })
      return
    }

    const promises = Array.from(this.providers.values()).map(provider => 
      this.safeCall(provider, 'track', event, properties)
    )
    
    await Promise.allSettled(promises)
  }

  async identify(properties: Record<string, any>): Promise<void> {
    if (!this.config.enabled) return

    if (!this.isInitialized) {
      this.eventQueue.push({ method: 'identify', args: [properties] })
      return
    }

    const promises = Array.from(this.providers.values()).map(provider =>
      this.safeCall(provider, 'identify', properties)
    )

    await Promise.allSettled(promises)
  }

  setUserId(userId: string): void {
    if (!this.config.enabled) return

    if (!this.isInitialized) {
      this.eventQueue.push({ method: 'setUserId', args: [userId] })
      return
    }

    this.providers.forEach(provider => {
      try {
        provider.setUserId(userId)
      } catch (error) {
        console.error(`[Analytics] ${provider.name} setUserId failed:`, error)
      }
    })
  }

  // Provider management
  private async createProvider(name: string): Promise<AnalyticsProvider | null> {
    try {
      switch (name) {
        case 'console':
          const { ConsoleProvider } = await import('./providers/console')
          return new ConsoleProvider()
        case 'posthog':
          const { PostHogProvider } = await import('./providers/posthog')
          return new PostHogProvider()
        default:
          console.warn(`[Analytics] Unknown provider: ${name}`)
          return null
      }
    } catch (error) {
      console.error(`[Analytics] Failed to create provider ${name}:`, error)
      return null
    }
  }

  private async processEventQueue(): Promise<void> {
    if (this.eventQueue.length === 0) return

    if (this.config.debug) {
      console.log(`[Analytics] Processing ${this.eventQueue.length} queued events`)
    }

    for (const queueItem of this.eventQueue) {
      try {
        switch (queueItem.method) {
          case 'track':
            await this.track(queueItem.args[0], queueItem.args[1])
            break
          case 'identify':
            await this.identify(queueItem.args[0])
            break
          case 'setUserId':
            this.setUserId(queueItem.args[0])
            break
        }
      } catch (error) {
        console.error(`[Analytics] Failed to process queued ${queueItem.method}:`, error)
      }
    }

    this.eventQueue = []
  }

  private async safeCall(provider: AnalyticsProvider, method: string, ...args: any[]): Promise<void> {
    try {
      if (!provider.isReady()) {
        if (this.config.debug) {
          console.warn(`[Analytics] Provider ${provider.name} not ready for ${method}`)
        }
        return
      }

      await (provider as any)[method](...args)
    } catch (error) {
      console.error(`[Analytics] ${provider.name} ${method} failed:`, error)
    }
  }

  // Utility methods
  isReady(): boolean {
    return this.isInitialized
  }

  getProviders(): string[] {
    return Array.from(this.providers.keys())
  }

  getProvider(name: string): AnalyticsProvider | undefined {
    return this.providers.get(name)
  }
}
