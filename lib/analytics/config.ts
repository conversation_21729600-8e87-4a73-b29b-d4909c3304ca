import type { AnalyticsConfig } from './types'

export const ANALYTICS_CONFIG: AnalyticsConfig = {
  enabled: true,
  debug: process.env.NODE_ENV === 'development',

  providers: [
    {
      name: 'console',
      enabled: false,
      config: {}
    },
    {
      name: 'posthog',
      enabled: !!(process.env.NEXT_PUBLIC_POSTHOG_KEY && process.env.NEXT_PUBLIC_POSTHOG_HOST),
      config: {
        apiKey: process.env.NEXT_PUBLIC_POSTHOG_KEY,
        host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
        proxyUrl: '/trade',
      }
    }
  ]
} as const
