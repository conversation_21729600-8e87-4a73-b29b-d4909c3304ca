import type { AnalyticsProvider } from '../types'

/**
 * PostHog Analytics Provider
 * Integrates PostHog analytics with the unified analytics system
 */
export class PostHogProvider implements AnalyticsProvider {
  name = 'posthog'
  private isInitialized = false
  private posthog: any = null
  private userId: string | null = null

  async initialize(config: { apiKey: string; host: string, proxyUrl?: string }): Promise<void> {
    if (this.isInitialized) return

    try {
      // Dynamic import to ensure client-side only
      const { default: posthog } = await import('posthog-js')

      // Initialize PostHog
      posthog.init(config.apiKey, {
        api_host: config.proxyUrl || config.host,
        ui_host: config.host,
        // PostHog configuration options
        capture_pageview: false, // We'll handle pageviews manually
        capture_pageleave: true,
        loaded: () => {
          console.log('[PostHog] Initialized successfully')
        },
        // Privacy and compliance settings
        opt_out_capturing_by_default: false,

        // Cross-domain tracking
        cross_subdomain_cookie: false,

        autocapture: false, // Disable autocapture to reduce requests
        // Debugging
        debug: process.env.NODE_ENV === 'development',
      })

      this.posthog = posthog
      this.isInitialized = true

      // Set user ID if it was set before initialization
      if (this.userId) {
        this.posthog.identify(this.userId)
      }

    } catch (error) {
      console.error('[PostHog] Failed to initialize:', error)
      throw error
    }
  }

  isReady(): boolean {
    return this.isInitialized && this.posthog !== null
  }

  async track(event: string, properties?: Record<string, any>): Promise<void> {
    if (!this.isReady()) {
      return
    }

    try {
      // Clean and prepare properties
      const cleanProperties = this.sanitizeProperties(properties)

      // Track the event
      this.posthog.capture(event, cleanProperties)
    } catch (error) {

    }
  }

  async identify(properties: Record<string, any>): Promise<void> {
    if (!this.isReady()) {
      console.warn('[PostHog] Provider not ready, skipping identify call')
      return
    }

    try {
      // Clean and prepare properties
      const cleanProperties = this.sanitizeProperties(properties)
      
      // Identify the user with properties
      if (this.userId) {
        this.posthog.identify(this.userId, cleanProperties)
      } else {
        this.posthog.identify(cleanProperties)
      }
    } catch (error) {
      console.error('[PostHog] Identify failed:', error)
    }
  }

  setUserId(userId: string): void {
    this.userId = userId

    if (this.isReady()) {
      try {
        this.posthog.identify(userId)
      } catch (error) {
        console.error('[PostHog] SetUserId failed:', error)
      }
    }
  }

  // Additional PostHog-specific methods (optional)
  
  /**
   * Reset the user (useful for logout)
   */
  reset(): void {
    if (this.isReady()) {
      this.posthog.reset()
      this.userId = null
    }
  }

  /**
   * Set user properties
   */
  setPersonProperties(properties: Record<string, any>): void {
    if (this.isReady()) {
      const cleanProperties = this.sanitizeProperties(properties)
      this.posthog.people.set(cleanProperties)
    }
  }

  /**
   * Register super properties (sent with every event)
   */
  registerSuperProperties(properties: Record<string, any>): void {
    if (this.isReady()) {
      const cleanProperties = this.sanitizeProperties(properties)
      this.posthog.register(cleanProperties)
    }
  }

  // Private helper methods

  private sanitizeProperties(properties?: Record<string, any>): Record<string, any> {
    if (!properties) return {}

    // Remove undefined values and sanitize the properties
    const sanitized: Record<string, any> = {}
    
    for (const [key, value] of Object.entries(properties)) {
      if (value !== undefined && value !== null) {
        // Convert dates to ISO strings
        if (value instanceof Date) {
          sanitized[key] = value.toISOString()
        }
        // Ensure strings are properly encoded
        else if (typeof value === 'string') {
          sanitized[key] = value.trim()
        }
        // Keep other primitive types as-is
        else if (typeof value === 'number' || typeof value === 'boolean') {
          sanitized[key] = value
        }
        // Handle objects and arrays (stringify if needed)
        else if (typeof value === 'object') {
          try {
            sanitized[key] = JSON.parse(JSON.stringify(value))
          } catch {
            sanitized[key] = String(value)
          }
        }
        else {
          sanitized[key] = value
        }
      }
    }

    return sanitized
  }
}
